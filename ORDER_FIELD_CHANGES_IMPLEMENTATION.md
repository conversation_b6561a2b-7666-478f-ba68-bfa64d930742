# Order Field Changes Implementation

## Overview

This implementation adds field-level change tracking to the order update functionality. When a user updates an order, the system now identifies and returns exactly which fields were changed, along with their previous and new values.

## Implementation Details

### 1. Service Layer Changes (`src/orders/services.py`)

#### New Methods Added:

- **`_compare_field_changes()`**: Compares individual field values and returns change information if different
- **`_get_order_field_changes()`**: Compares entire order objects and returns all field changes

#### Modified Methods:

- **`update_order()`**: Now returns a tuple `(OrderResponse, field_changes_dict)` instead of just `OrderResponse`
- **`AbstractOrdersService.update_order()`**: Updated abstract method signature to match

### 2. API Schema Changes (`src/api/orders/schemas.py`)

#### New Schemas Added:

- **`FieldChange`**: Represents a single field change with `current_value` and `new_value`
- **`UpdateOrderResponse`**: New response schema that includes both order ID and field changes

#### Key Features:

- Includes a `from_service_response()` class method for easy conversion from service layer response
- Uses camelCase naming convention for API consistency

### 3. Endpoint Changes (`src/api/orders/endpoints.py`)

#### Modified Endpoints:

- **`PUT /orders/{order_uuid}`**: Now returns `UpdateOrderResponse` with field changes instead of just `OrderResponse`

### 4. Proxy Layer Changes (`src/orders/proxies.py`)

#### Updated Methods:

- **`OrdersServiceAuthProxy.update_order()`**: Updated to handle new return type from service layer

### 5. Test Updates

#### Updated Test Files:

- **`tests/unit/orders/test_service.py`**: Updated to handle tuple return type
- **`tests/unit/orders/test_endpoints.py`**: Updated to test new response schema

## Field Change Detection

### Supported Field Categories:

1. **Customer Details**:
   - `customer_account_name`
   - `person_placing_order`
   - `customer_reference`
   - `customer_email`
   - `customer_contact_no`
   - `customer_account_logo_url`

2. **Shipping Details**:
   - `contact_name`
   - `address_line1`
   - `address_line2`
   - `city`
   - `state_or_region`
   - `postal_code`
   - `country`
   - `other_information`

3. **Order Items**:
   - `order_item_{sim_type}_quantity` (e.g., `order_item_STANDARD_quantity`)

### Change Detection Logic:

- Compares string representations of values to handle different data types
- Handles `None` values appropriately
- For order items, tracks quantity changes per SIM type
- New items show as changing from 0 to the new quantity
- Removed items show as changing from old quantity to 0

## API Response Format

### Example Response:

```json
{
  "id": "ba6f3a9d-9d73-4620-935b-bbf9dd9921ab",
  "fieldChanges": {
    "customerEmail": {
      "currentValue": "<EMAIL>",
      "newValue": "<EMAIL>"
    },
    "customerAccountName": {
      "currentValue": "Old Company",
      "newValue": "New Company"
    },
    "orderItemStandardQuantity": {
      "currentValue": 10,
      "newValue": 15
    }
  }
}
```

## Usage Example

### Service Layer:

```python
# Update an order and get field changes
order_response, field_changes = orders_service.update_order(
    order_id=order_uuid,
    order=updated_order_data
)

# field_changes is a dictionary like:
# {
#     "customer_email": {
#         "current_value": "<EMAIL>",
#         "new_value": "<EMAIL>"
#     }
# }
```

### API Endpoint:

```python
# The endpoint automatically converts to the proper response format
return UpdateOrderResponse.from_service_response(order_response, field_changes)
```

## Benefits

1. **Audit Trail**: Clear visibility into what exactly changed during an order update
2. **User Feedback**: Users can see exactly what fields were modified
3. **Integration Ready**: Other systems can easily consume field change information
4. **Backward Compatible**: Existing error handling and validation logic remains unchanged

## Testing

The implementation includes:

- Unit tests for the service layer field change detection
- Integration tests for the API endpoint
- Demonstration script showing field change tracking in action

## Future Enhancements

Potential improvements could include:

1. **Audit Logging**: Store field changes in an audit log table
2. **Change Notifications**: Send notifications when specific fields change
3. **Change Validation**: Add business rules for which fields can be changed together
4. **Change History**: Track multiple changes over time for the same order
