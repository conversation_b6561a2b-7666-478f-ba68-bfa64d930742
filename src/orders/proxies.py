from typing import List, <PERSON><PERSON>
from uuid import UUID

from api.orders.schemas import UpdateOrderStatusRequest, UpdateOrderStatusResponse
from app.config import logger
from auth.dto import AuthenticatedUser
from auth.exceptions import ForbiddenError, Unauthorized
from auth.permissions import (
    is_account_admin,
    is_distributor_admin,
    is_distributor_client,
)
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from orders.adapters.exceptions import StatusError
from orders.constants import OrderStatus
from orders.domain import model
from orders.services import AbstractOrdersService


class OrdersServiceAuthProxy(AbstractOrdersService):
    def __init__(
        self,
        orders_service: AbstractOrdersService,
        user: AuthenticatedUser,
    ) -> None:
        self.orders_service = orders_service
        self.user = user

    def create_order(
        self, order: model.OrderRequest, user: str | None = None
    ) -> model.OrderResponse:
        if is_distributor_client(self.user):
            # Only check account if organization is a Client
            if (
                self.user.organization.account.id  # type: ignore
                != order.customer_details.customer_account_id  # type: ignore
            ):
                logger.warning("User is not belongs to account in order.")
                raise ForbiddenError

        return self.orders_service.create_order(order=order, user=self.user.email)

    def update_order_status(
        self,
        order_uuid: UUID,
        update_data: UpdateOrderStatusRequest,
        user: str | None = None,
    ) -> UpdateOrderStatusResponse:

        # get order current details by order id
        order = self.orders_service.get_order_details(order_uuid)
        # check user is accout admin
        is_account_admin_user = is_account_admin(
            self.user, order.customer_details.customer_account_id
        )
        # check user is distributor admin
        is_distributor_admin_user = is_distributor_admin(self.user)

        if not (is_account_admin_user or is_distributor_admin_user):
            logger.warning("User is not authorised to change status.")
            raise Unauthorized

        if (
            is_account_admin_user
            and order.status != OrderStatus.PENDING.value
            and update_data.status == OrderStatus.CANCELLED.value
        ):
            logger.warning(
                f"Client admin is not authorised for change status to "
                f"{update_data.status}."
            )
            raise StatusError("Client admin is not authorised for change status.")

        if order.status == OrderStatus.SHIPPED.value:
            logger.warning(
                f"Distributor admin is not authorised for change status. "
                f"from {OrderStatus.SHIPPED.value} to {update_data.status}"
            )
            raise StatusError("Order is already shipped.")

        return self.orders_service.update_order_status(
            order_uuid=order_uuid, update_data=update_data, user=self.user.email
        )

    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Tuple[List[model.OrdersData], int]:

        return self.orders_service.get_orders(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

    def get_order_details(self, order_uuid: UUID) -> model.OrderDetailsResponse:
        return self.orders_service.get_order_details(order_uuid=order_uuid)

    def update_order(
        self, order_id: UUID, order: model.OrderRequest, user: str | None = None
    ) -> model.OrderResponse:
        if is_distributor_client(self.user):
            # Only check account if organization is a Client
            order_details = self.orders_service.get_order_details(order_id)
            if (
                self.user.organization.account.id  # type: ignore
                != order_details.customer_details.customer_account_id  # type: ignore
            ):
                logger.warning("User is not belongs to account in order.")
                raise ForbiddenError

        return self.orders_service.update_order(
            order_id=order_id,
            order=order,
            user=self.user.email,
        )
