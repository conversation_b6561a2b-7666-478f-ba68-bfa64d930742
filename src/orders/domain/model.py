from dataclasses import dataclass
from datetime import datetime
from typing import List
from uuid import UUID

from pydantic import EmailStr, Field

from common.types import FormFactor


@dataclass
class Order:
    order_by: str
    status: str | None = None
    uuid: UUID | None = None
    id: int | None = None
    order_date: datetime | None = None
    order_id: str | None = None  # Added for string order_id


@dataclass
class OrderCustomer:
    customer_id: UUID
    customer_email: EmailStr
    customer_contact_no: str
    customer_account_id: int
    customer_account_name: str = Field(min_length=1, max_length=60)
    person_placing_order: str | None = None
    customer_reference: str | None = None
    uuid: UUID | None = None
    id: int | None = None
    customer_account_logo_url: str | None = None
    order_id: UUID | None = None


@dataclass
class OrderShippingDetails:
    contact_name: str
    address_line1: str
    address_line2: str
    city: str
    state_or_region: str
    postal_code: str
    country: str
    uuid: UUID | None = None
    id: int | None = None
    other_information: str | None = None
    order_id: UUID | None = None


@dataclass
class OrderItem:
    uuid: UUID | None = None
    id: int | None = None
    order_id: UUID | None = None
    sim_type: FormFactor | None = None
    quantity: int | None = None


@dataclass
class OrderStatusHistory:
    uuid: UUID | None = None
    id: int | None = None
    status_name: str | None = None
    status_date: datetime | None = None
    comments: str | None = None
    added_by: str | None = None
    order_id: UUID | None = None


@dataclass
class OrderTracking:
    uuid: UUID | None = None
    id: int | None = None
    reference_id: str | None = None
    reference_url: str | None = None
    order_id: UUID | None = None


@dataclass
class RejectReason:
    uuid: UUID | None = None
    id: int | None = None
    comment: str | None = None
    order_id: UUID | None = None


@dataclass
class OrderRequest:
    order_by: str
    customer_details: OrderCustomer
    order_items: List[OrderItem]
    shipping_details: OrderShippingDetails


@dataclass
class OrderResponse:
    id: UUID | None = None


@dataclass
class OrdersData:
    order_id: str  # Changed from UUID to str
    order_uuid: UUID
    order_by: str
    customer_account_name: str
    order_date: datetime
    person_placing_order: str
    customer_email: str
    customer_phone: str
    status: str
    order_item: List[OrderItem]
    customer_account_logo_url: str | None = None


@dataclass
class OrderDetailsResponse:
    order_by: str
    order_id: str  # Changed from UUID to str
    order_uuid: str
    id: int
    order_date: datetime
    status: str
    customer_details: OrderCustomer
    shipping_details: OrderShippingDetails
    order_items: List[OrderItem]
    order_tracking: OrderTracking | None = None
    comments: str | None = None


@dataclass
class OrderAudit:
    uuid: UUID
    order_id: str
    order_uuid: UUID
    account_id: int
    request_type: str
    prior_value: str | None
    new_value: str | None
    field: str
    action: str
    client_ip: str | None = None
    created_by: str | None = None
