import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List
from uuid import UUID, uuid4

from pydantic import EmailStr
from sqlalchemy.exc import SQLAlchemyError

from api.orders.schemas import UpdateOrderStatusRequest, UpdateOrderStatusResponse
from app.config import logger, settings
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import ParsingError
from common.searching import Searching
from common.types import FormFactor
from mail_delivery.service import AbstractMailService
from orders.adapters.repository import AbstractOrdersRepository
from orders.constants import OrderStatus, RecipientType
from orders.domain import model
from orders.email_template import OrderData, OrderEmailBuilder
from sim.exceptions import NotFound
from streaming.streaming import AbstractKafkaAPI


class AbstractOrdersService(ABC):
    @abstractmethod
    def create_order(
        self, order: model.OrderRequest, user: str | None = None
    ) -> model.OrderResponse:
        ...

    @abstractmethod
    def update_order_status(
        self,
        order_uuid: UUID,
        update_data: UpdateOrderStatusRequest,
        user: str | None = None,
    ) -> UpdateOrderStatusResponse:
        ...

    @abstractmethod
    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[List[model.OrdersData], int]:
        ...

    @abstractmethod
    def get_order_details(self, order_uuid: UUID) -> model.OrderDetailsResponse:
        ...

    @abstractmethod
    def update_order(
        self,
        order_id: UUID,
        order: model.OrderRequest,
        user: str | None = None,
    ) -> model.OrderResponse:
        ...


class OrdersService(AbstractOrdersService):
    def __init__(
        self,
        orders_repository: AbstractOrdersRepository,
        mail_service: AbstractMailService,
        streaming_service: AbstractKafkaAPI | None = None,
    ):
        self.orders_repository = orders_repository
        self.mail_service = mail_service
        self.streaming_service = streaming_service

    def _compare_field_changes(
        self,
        current_value: Any,
        new_value: Any,
        field_name: str
    ) -> Dict[str, Dict[str, Any]] | None:
        """
        Compare current and new values for a field and return change info if different.

        Args:
            current_value: The current value of the field
            new_value: The new value of the field
            field_name: The name of the field being compared

        Returns:
            Dict with field change info or None if no change
        """
        # Handle None values
        if current_value is None and new_value is None:
            return None

        # Convert values to strings for comparison to handle different types
        current_str = str(current_value) if current_value is not None else None
        new_str = str(new_value) if new_value is not None else None

        if current_str != new_str:
            return {
                field_name: {
                    "current_value": current_value,
                    "new_value": new_value
                }
            }
        return None

    def _get_order_field_changes(
        self,
        current_order: model.OrderDetailsResponse,
        new_order: model.OrderRequest
    ) -> Dict[str, Dict[str, Any]]:
        """
        Compare current order with new order data and return all field changes.

        Args:
            current_order: Current order details from database
            new_order: New order data from update request

        Returns:
            Dictionary with all changed fields in format: {field: {current_value: , new_value: }}
        """
        changes = {}

        # Compare customer details
        current_customer = current_order.customer_details
        new_customer = new_order.customer_details

        customer_fields = [
            ('customer_account_name', current_customer.customer_account_name, new_customer.customer_account_name),
            ('person_placing_order', current_customer.person_placing_order, new_customer.person_placing_order),
            ('customer_reference', current_customer.customer_reference, new_customer.customer_reference),
            ('customer_email', current_customer.customer_email, new_customer.customer_email),
            ('customer_contact_no', current_customer.customer_contact_no, new_customer.customer_contact_no),
            ('customer_account_logo_url', current_customer.customer_account_logo_url, new_customer.customer_account_logo_url),
        ]

        for field_name, current_val, new_val in customer_fields:
            field_change = self._compare_field_changes(current_val, new_val, field_name)
            if field_change:
                changes.update(field_change)

        # Compare shipping details
        current_shipping = current_order.shipping_details
        new_shipping = new_order.shipping_details

        shipping_fields = [
            ('contact_name', current_shipping.contact_name, new_shipping.contact_name),
            ('address_line1', current_shipping.address_line1, new_shipping.address_line1),
            ('address_line2', current_shipping.address_line2, new_shipping.address_line2),
            ('city', current_shipping.city, new_shipping.city),
            ('state_or_region', current_shipping.state_or_region, new_shipping.state_or_region),
            ('postal_code', current_shipping.postal_code, new_shipping.postal_code),
            ('country', current_shipping.country, new_shipping.country),
            ('other_information', current_shipping.other_information, new_shipping.other_information),
        ]

        for field_name, current_val, new_val in shipping_fields:
            field_change = self._compare_field_changes(current_val, new_val, field_name)
            if field_change:
                changes.update(field_change)

        # Compare order items (more complex as it's a list)
        current_items = {item.sim_type: item.quantity for item in current_order.order_items}
        new_items = {item.sim_type: item.quantity for item in new_order.order_items}

        # Check for changed quantities
        all_sim_types = set(current_items.keys()) | set(new_items.keys())
        for sim_type in all_sim_types:
            current_qty = current_items.get(sim_type, 0)
            new_qty = new_items.get(sim_type, 0)

            field_name = f"order_item_{sim_type}_quantity"
            field_change = self._compare_field_changes(current_qty, new_qty, field_name)
            if field_change:
                changes.update(field_change)

        return changes

    def _create_order_audit_logs(
        self,
        order_uuid: UUID,
        order_id: str,
        account_id: int,
        field_changes: Dict[str, Dict[str, Any]],
        client_ip: str | None = None,
        created_by: str | None = None,
    ) -> List[model.OrderAudit]:
        """
        Create audit log entries for order field changes.

        Args:
            order_uuid: UUID of the order
            order_id: String ID of the order
            account_id: Account ID associated with the order
            field_changes: Dictionary of field changes
            client_ip: Client IP address
            created_by: User who made the changes

        Returns:
            List of OrderAudit objects
        """
        audit_logs = []

        for field_name, change_info in field_changes.items():
            audit_logs.append(
                model.OrderAudit(
                    uuid=uuid4(),
                    order_id=order_id,
                    order_uuid=order_uuid,
                    account_id=account_id,
                    request_type="Update Order",
                    prior_value=str(change_info["current_value"]) if change_info["current_value"] is not None else None,
                    new_value=str(change_info["new_value"]) if change_info["new_value"] is not None else None,
                    field=field_name,
                    action="Updated",
                    client_ip=client_ip,
                    created_by=created_by,
                )
            )

        return audit_logs

    def __constructing_order_data_for_mail(self, order_uuid: UUID) -> OrderData:
        order_details = self.orders_repository.get_order_details(order_uuid=order_uuid)
        sim_type_wise_qty: dict = {
            item.sim_type: item.quantity for item in order_details.order_items
        }
        tracking_info = {}
        if order_details.order_tracking is not None:
            tracking_info = {
                "tracking_url": order_details.order_tracking.reference_url,
                "tracking_ref": order_details.order_tracking.reference_id,
            }
        order_data: OrderData = OrderData(
            order_id=str(order_details.order_id),
            order_uuid=str(order_uuid),
            order_status=order_details.status,  # type: ignore
            order_date=order_details.order_date.strftime("%Y-%m-%d %H:%M"),
            sim_type_wise_qty=sim_type_wise_qty,
            customer_account_ref=(
                order_details.customer_details.customer_reference  # type: ignore
            ),  # type: ignore
            customer_contact_name=order_details.shipping_details.contact_name,
            person_name=(
                order_details.customer_details.person_placing_order  # type: ignore
            ),  # type: ignore
            person_email=order_details.customer_details.customer_email,
            phone_number=order_details.customer_details.customer_contact_no,
            shipping_address_address1=order_details.shipping_details.address_line1,
            shipping_address_address2=order_details.shipping_details.address_line2,
            city=order_details.shipping_details.city,
            state=order_details.shipping_details.state_or_region,
            postal_code=order_details.shipping_details.postal_code,
            country=order_details.shipping_details.country,
            additional_info=order_details.shipping_details.other_information,
            comments=order_details.comments,
            web_link=(
                f"{settings.APP_LANDING_PAGE_URL}"
                f"/sim-management?tab=sim-order&page=1&pageSize=10"
                f"&search={str(order_details.order_id)}"
            ),
            **tracking_info,
        )
        return order_data

    def __send_mail(self, order_uuid: UUID):
        order_data: OrderData = self.__constructing_order_data_for_mail(
            order_uuid=order_uuid
        )
        user_email_builder = OrderEmailBuilder(
            order_data=order_data, recipient_type=RecipientType.USER
        )
        manager_email_builder = OrderEmailBuilder(
            order_data=order_data, recipient_type=RecipientType.MANAGER
        )
        try:
            self.mail_service.send_mail(
                subject=user_email_builder.get_subject(),
                name_from="BT IoT Portal",
                recipients=[order_data.person_email],
                html_body=user_email_builder.build_email_body(),
            )
        except Exception as ex:
            logging.error(f"getting Error while sending mail: {ex}")
        if not settings.BT_MANAGER_MAIL_ID:
            raise ValueError(
                "Can not sent mail to BT Manager Please set BT_MANAGER_MAIL_ID"
            )
        if order_data.order_status not in [
            OrderStatus.PENDING.value,
            OrderStatus.CANCELLED.value,
        ]:
            return  # Skip sending to manager
        try:
            self.mail_service.send_mail(
                subject=manager_email_builder.get_subject(),
                name_from="BT IoT Portal",
                recipients=[
                    EmailStr(email) for email in settings.BT_MANAGER_MAIL_ID.split(";")
                ],
                html_body=manager_email_builder.build_email_body(),
            )
        except Exception as ex:
            logging.error(f"getting Error while sending mail: {ex}")

    def create_order(
        self, order: model.OrderRequest, user: str | None = None
    ) -> model.OrderResponse:
        try:
            # Ensure order_items is not empty
            # if not order.order_items or len(order.order_items) == 0:
            #     raise ParsingError("Order must have at least one item.")
            for item in order.order_items:
                if item.sim_type in [
                    FormFactor.eSIM_MFF2.value,
                    FormFactor.eSIM_MFF2_eUICC.value,
                ] and (
                    item.quantity is None or item.quantity < settings.MIN_E_SIM_QUANTITY
                ):
                    raise ParsingError(
                        (
                            (
                                f"Minimum allowed quantity for {item.sim_type} is "
                                f"{settings.MIN_E_SIM_QUANTITY}. "
                                f"Requested: {item.quantity}"
                            )
                        )
                    )
            # Adding order and return uuid
            order_response = self.orders_repository.create_order(order=order)
            if not order_response.id:
                logger.error("Failed to create order, no response received.")
                raise ParsingError("Failed to create order, please try again.")
            # Adding order and customer details
            self.orders_repository.add_order_customer(
                customer_details=order.customer_details, order_uuid=order_response.id
            )
            # Adding order shipping details
            self.orders_repository.add_order_shipping(
                shipping_details=order.shipping_details, order_uuid=order_response.id
            )

            # Adding order item
            self.orders_repository.add_order_item(
                order_items=order.order_items, order_uuid=order_response.id
            )

            self.orders_repository.commit_order()
            self.__send_mail(order_uuid=order_response.id)
            return order_response
        except SQLAlchemyError as e:
            logger.error(f"Error creating order: {e}")
            raise

    def update_order_status(
        self,
        order_uuid: UUID,
        update_data: UpdateOrderStatusRequest,
        user: str | None = None,
    ) -> UpdateOrderStatusResponse:
        order = self.orders_repository.get_order(order_uuid)
        # Check if the order exists
        if not order:
            raise NotFound("Data not found.")
        else:
            current_status = order.status
            new_status = update_data.status
        # Define valid status transitions
        allowed_transitions: dict[str, list[str]] = {
            OrderStatus.PENDING.value: [
                OrderStatus.CANCELLED.value,
                OrderStatus.APPROVED.value,
                OrderStatus.ONHOLD.value,
            ],
            OrderStatus.ONHOLD.value: [
                OrderStatus.CANCELLED.value,
                OrderStatus.APPROVED.value,
            ],
            OrderStatus.APPROVED.value: [
                OrderStatus.CANCELLED.value,
                OrderStatus.SHIPPED.value,
            ],
            OrderStatus.SHIPPED.value: [],
        }

        # Validate transition
        if current_status in allowed_transitions:
            if new_status not in allowed_transitions[current_status]:
                raise ParsingError(
                    (
                        f"Invalid status transition from {current_status} "
                        f"to {new_status}"
                    )
                )
        else:
            # If current status is not in allowed transitions
            # disallow changes
            raise ParsingError(
                (
                    f"Cannot update status from {current_status}. "
                    f"Status updates not allowed to {new_status}."
                )
            )

        # Validate tracking info - only allowed for SHIPPED status
        if update_data.status == OrderStatus.SHIPPED.value:
            if not update_data.tracking:
                raise ParsingError(
                    "Tracking information is required for shipped orders"
                )
            if not update_data.tracking.reference_id:
                raise ParsingError(
                    "Tracking reference ID is required for shipped orders"
                )
            if not update_data.tracking.reference_url:
                raise ParsingError(
                    "Tracking reference URL is required for shipped orders"
                )
            self.orders_repository.add_order_tracking(
                order_uuid=order_uuid, tracking=update_data.tracking
            )
        elif update_data.tracking:
            raise ParsingError(
                "Tracking information is only allowed for shipped orders"
            )

        if (
            update_data.status == OrderStatus.CANCELLED.value
            or update_data.status == OrderStatus.ONHOLD.value
        ):
            if not update_data.comments:
                raise ParsingError(
                    f"Comments are required for orders with status {update_data.status}"
                )
            self.orders_repository.add_reject_reason(
                order_uuid=order_uuid, update_data=update_data
            )
        elif update_data.comments:
            raise ParsingError("Comments are only allowed for cancelled orders")

        # Update order status
        order_response = self.orders_repository.update_order_status(
            order_uuid=order_uuid, update_data=update_data
        )
        self.__send_mail(order_uuid=order_response.id)
        return order_response

    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[List[model.OrdersData], int]:

        response = self.orders_repository.get_orders(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        count = self.orders_repository.get_orders_count(
            account_id=account_id,
            searching=searching,
        )

        return response, count

    def get_order_details(self, order_uuid: UUID) -> model.OrderDetailsResponse:
        order_details = self.orders_repository.get_order_details(order_uuid=order_uuid)
        return order_details

    def update_order(
        self,
        order_id: UUID,
        order: model.OrderRequest,
        user: str | None = None,
        client_ip: str | None = None,
    ) -> model.OrderResponse:
        """
        Update an order and create audit logs for field changes.

        Args:
            order_id: UUID of the order to update
            order: New order data
            user: User making the update
            client_ip: Client IP address

        Returns:
            OrderResponse
        """
        try:
            # Ensure order_items is not empty
            if not order.order_items or len(order.order_items) == 0:
                raise ParsingError("Order must have at least one item.")

            # Fetch the current order to check its status
            current_order = self.orders_repository.get_order(order_uuid=order_id)
            if not current_order:
                raise NotFound("Please try again!")
            if current_order.status != OrderStatus.PENDING.value:
                raise ParsingError(
                    f"Order can only be updated if it is in '"
                    f"{OrderStatus.PENDING.value}' status. "
                    f"Current status: {current_order.status}"
                )

            # Get current order details for comparison
            current_order_details = self.orders_repository.get_order_details(
                order_uuid=order_id
            )

            # Track field changes before updating
            field_changes = self._get_order_field_changes(
                current_order_details, order
            )

            # Update related tables
            self.orders_repository.update_order_customer(
                customer_details=order.customer_details, order_id=order_id
            )
            self.orders_repository.update_order_shipping(
                shipping_details=order.shipping_details, order_id=order_id
            )
            self.orders_repository.update_order_item(
                order_items=order.order_items, order_id=order_id
            )
            self.orders_repository.commit_order()
            self.__send_mail(order_uuid=order_id)

            logger.info(f"Order updated successfully and Email Triggered: {order_id}")
            logger.info(f"Field changes detected: {field_changes}")

            return model.OrderResponse(id=order_id), field_changes
        except SQLAlchemyError as e:
            logger.error(f"Error updating order: {e}")
            raise
