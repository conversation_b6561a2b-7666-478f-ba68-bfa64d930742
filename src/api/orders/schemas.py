from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import EmailStr, Field, parse_obj_as

from api.orders.examples import CREATE_ORDER_REQUEST, CREATE_ORDER_RESPONSE
from api.schema_types import CamelBaseModel
from common.types import FormFactor
from orders.constants import OrderStatus
from orders.domain import model


class OrderCustomer(CamelBaseModel):
    customer_id: UUID
    customer_email: EmailStr = Field()
    customer_contact_no: str = Field()
    customer_account_name: str = Field(min_length=1, max_length=60)
    person_placing_order: str = Field()
    customer_reference: str = Field()
    customer_account_id: int = Field()
    customer_account_logo_url: str | None
    order_id: UUID | None = None

    def to_model(self) -> model.OrderCustomer:
        return model.OrderCustomer(
            customer_id=self.customer_id,
            customer_email=self.customer_email,
            customer_contact_no=self.customer_contact_no,
            customer_account_name=self.customer_account_name,
            person_placing_order=self.person_placing_order,
            customer_reference=self.customer_reference,
            customer_account_id=self.customer_account_id,
            customer_account_logo_url=self.customer_account_logo_url,
            order_id=self.order_id,
        )


class OrderShippingDetails(CamelBaseModel):
    contact_name: str = Field()
    address_line1: str = Field()
    address_line2: str
    city: str = Field()
    state_or_region: str = Field()
    postal_code: str = Field()
    country: str = Field()
    other_information: str | None = None
    order_id: UUID | None = None

    def to_model(self) -> model.OrderShippingDetails:
        return model.OrderShippingDetails(
            contact_name=self.contact_name,
            address_line1=self.address_line1,
            address_line2=self.address_line2,
            city=self.city,
            state_or_region=self.state_or_region,
            postal_code=self.postal_code,
            country=self.country,
            other_information=self.other_information,
            order_id=self.order_id,
        )


class OrderItem(CamelBaseModel):
    sim_type: FormFactor = Field()
    quantity: int = Field()

    def to_model(self) -> model.OrderItem:
        return model.OrderItem(sim_type=self.sim_type, quantity=self.quantity)


class CreateOrderRequest(CamelBaseModel):
    order_by: str
    customer_details: OrderCustomer
    shipping_details: OrderShippingDetails
    order_items: List[OrderItem] = Field(min_items=1)

    class Config:
        schema_extra = {"example": CREATE_ORDER_REQUEST}

    def to_model(self) -> model.OrderRequest:
        # Ensure order_status_history is never None, provide a default if missing
        return model.OrderRequest(
            order_by=self.order_by,
            customer_details=OrderCustomer.to_model(self.customer_details),
            shipping_details=OrderShippingDetails.to_model(self.shipping_details),
            order_items=[OrderItem.to_model(item) for item in self.order_items],
        )


# Example usage
parsed_order = parse_obj_as(CreateOrderRequest, CREATE_ORDER_REQUEST)


class CreateOrderResponse(CamelBaseModel):
    id: UUID

    @classmethod
    def to_model(cls, order: model.OrderResponse) -> "CreateOrderResponse":
        return cls(
            id=order.id,
        )

    class Config:
        schema_extra = {"example": CREATE_ORDER_RESPONSE}
        orm_mode = True


class TrackingInfo(CamelBaseModel):
    reference_id: str | None = None
    reference_url: str | None = None


class UpdateOrderStatusRequest(CamelBaseModel):
    status: OrderStatus
    tracking: TrackingInfo | None = None
    comments: str | None = Field(default=None, max_length=255)


class UpdateOrderStatusResponse(CamelBaseModel):
    id: UUID


class OrderCustomerResponse(CamelBaseModel):
    customer_id: UUID
    customer_email: str
    customer_contact_no: str
    customer_account_name: str
    person_placing_order: str
    customer_reference: str
    customer_account_id: int
    customer_account_logo_url: Optional[str] = None


class OrderShippingDetailsResponse(CamelBaseModel):
    contact_name: str
    address_line1: str
    address_line2: str
    city: str
    state_or_region: str
    postal_code: str
    country: str
    other_information: Optional[str] = None


class OrderItemResponse(CamelBaseModel):
    sim_type: FormFactor
    quantity: int


class OrderStatusHistoryResponse(CamelBaseModel):
    status_name: str
    status_date: datetime
    comments: Optional[str] = None


class OrderTrackingResponse(CamelBaseModel):
    reference_id: str
    reference_url: str


class OrdersDataResponse(CamelBaseModel):
    order_id: str
    order_uuid: UUID
    order_by: str
    customer_account_name: str
    order_date: datetime
    person_placing_order: str
    customer_email: str
    customer_phone: str
    status: str
    order_item: List[OrderItem]
    customer_account_logo_url: str | None = None

    @classmethod
    def from_model(cls, order_data: model.OrdersData) -> "OrdersDataResponse":
        return cls(
            order_id=order_data.order_id,
            order_uuid=order_data.order_uuid,
            order_by=order_data.order_by,
            customer_account_name=order_data.customer_account_name,
            customer_account_logo_url=order_data.customer_account_logo_url,
            order_date=order_data.order_date,
            customer_email=order_data.customer_email,
            person_placing_order=order_data.person_placing_order,
            customer_phone=order_data.customer_phone,
            status=order_data.status,
            order_item=order_data.order_item,
        )


class OrderDetailsResponse(CamelBaseModel):
    order_by: str
    order_id: str
    order_uuid: UUID
    order_date: datetime
    status: str
    customer_details: OrderCustomerResponse
    shipping_details: OrderShippingDetailsResponse
    order_items: List[OrderItemResponse]
    order_tracking: OrderTrackingResponse | None = None
    comments: str | None = None

    class Config:
        schema_extra = {"example": CREATE_ORDER_REQUEST}

    @classmethod
    def from_model(
        cls, order_details: model.OrderDetailsResponse
    ) -> "OrderDetailsResponse":
        cd = order_details.customer_details
        sd = order_details.shipping_details
        ot = order_details.order_tracking
        return cls(
            order_by=order_details.order_by,
            order_id=order_details.order_id,
            order_uuid=order_details.order_uuid,
            order_date=order_details.order_date,
            status=order_details.status,
            customer_details=OrderCustomerResponse(
                customer_id=cd.customer_id,
                customer_email=cd.customer_email,
                customer_contact_no=cd.customer_contact_no,
                customer_account_id=cd.customer_account_id,
                customer_account_name=cd.customer_account_name,
                person_placing_order=cd.person_placing_order,
                customer_reference=cd.customer_reference,
                customer_account_logo_url=cd.customer_account_logo_url,
            ),
            shipping_details=OrderShippingDetailsResponse(
                contact_name=sd.contact_name,
                address_line1=sd.address_line1,
                address_line2=sd.address_line2,
                city=sd.city,
                state_or_region=sd.state_or_region,
                postal_code=sd.postal_code,
                country=sd.country,
                other_information=sd.other_information,
            ),
            order_items=[
                OrderItemResponse(sim_type=item.sim_type, quantity=item.quantity)
                for item in order_details.order_items
            ],
            order_tracking=OrderTrackingResponse(
                reference_id=ot.reference_id,
                reference_url=ot.reference_url,
            )
            if ot
            else None,
            comments=order_details.comments,
        )
